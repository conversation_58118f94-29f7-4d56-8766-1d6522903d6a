"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/training/page",{

/***/ "(app-pages-browser)/./src/app/training/page.tsx":
/*!***********************************!*\
  !*** ./src/app/training/page.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TrainingPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_DocumentUpload__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/DocumentUpload */ \"(app-pages-browser)/./src/components/DocumentUpload.tsx\");\n/* harmony import */ var _components_TierEnforcement__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/TierEnforcement */ \"(app-pages-browser)/./src/components/TierEnforcement/index.ts\");\n/* harmony import */ var _hooks_useSubscription__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useSubscription */ \"(app-pages-browser)/./src/hooks/useSubscription.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction TrainingPage() {\n    _s();\n    // Subscription hook\n    const { subscriptionStatus } = (0,_hooks_useSubscription__WEBPACK_IMPORTED_MODULE_4__.useSubscription)();\n    // State management\n    const [customConfigs, setCustomConfigs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedConfigId, setSelectedConfigId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [trainingJobs, setTrainingJobs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [successMessage, setSuccessMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Prompt engineering form state\n    const [trainingPrompts, setTrainingPrompts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // Load existing training data for a configuration\n    const loadExistingTrainingData = async (configId)=>{\n        if (!configId) return;\n        try {\n            // Load training jobs\n            const jobsResponse = await fetch(\"/api/training/jobs?custom_api_config_id=\".concat(configId));\n            if (jobsResponse.ok) {\n                const jobs = await jobsResponse.json();\n                if (jobs.length > 0) {\n                    var _latestJob_training_data;\n                    const latestJob = jobs[0];\n                    // Load training prompts\n                    if ((_latestJob_training_data = latestJob.training_data) === null || _latestJob_training_data === void 0 ? void 0 : _latestJob_training_data.raw_prompts) {\n                        setTrainingPrompts(latestJob.training_data.raw_prompts);\n                    }\n                }\n            }\n        } catch (err) {\n            console.warn('Failed to load existing training data:', err);\n        }\n    };\n    // Fetch custom API configs on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TrainingPage.useEffect\": ()=>{\n            const fetchConfigs = {\n                \"TrainingPage.useEffect.fetchConfigs\": async ()=>{\n                    try {\n                        const response = await fetch('/api/custom-configs');\n                        if (!response.ok) {\n                            throw new Error('Failed to fetch configurations');\n                        }\n                        const data = await response.json();\n                        setCustomConfigs(data);\n                        if (data.length > 0) {\n                            setSelectedConfigId(data[0].id);\n                            loadExistingTrainingData(data[0].id);\n                        }\n                    } catch (err) {\n                        setError(\"Failed to load configurations: \".concat(err.message));\n                    }\n                }\n            }[\"TrainingPage.useEffect.fetchConfigs\"];\n            fetchConfigs();\n        }\n    }[\"TrainingPage.useEffect\"], []);\n    // Load training data when configuration changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TrainingPage.useEffect\": ()=>{\n            if (selectedConfigId) {\n                loadExistingTrainingData(selectedConfigId);\n            }\n        }\n    }[\"TrainingPage.useEffect\"], [\n        selectedConfigId\n    ]);\n    // Process training prompts into structured format\n    const processTrainingPrompts = (prompts)=>{\n        const processed = {\n            system_instructions: '',\n            examples: [],\n            behavior_guidelines: '',\n            general_instructions: ''\n        };\n        const lines = prompts.split('\\n').filter((line)=>line.trim());\n        for (const line of lines){\n            const trimmedLine = line.trim();\n            if (trimmedLine.startsWith('SYSTEM:')) {\n                processed.system_instructions += trimmedLine.replace('SYSTEM:', '').trim() + '\\n';\n            } else if (trimmedLine.startsWith('BEHAVIOR:')) {\n                processed.behavior_guidelines += trimmedLine.replace('BEHAVIOR:', '').trim() + '\\n';\n            } else if (trimmedLine.includes('→') || trimmedLine.includes('->')) {\n                // Parse example: \"User input → Expected response\"\n                const separator = trimmedLine.includes('→') ? '→' : '->';\n                const parts = trimmedLine.split(separator);\n                if (parts.length >= 2) {\n                    const input = parts[0].trim();\n                    const output = parts.slice(1).join(separator).trim();\n                    processed.examples.push({\n                        input,\n                        output\n                    });\n                }\n            } else if (trimmedLine.length > 0) {\n                // General instruction\n                processed.general_instructions += trimmedLine + '\\n';\n            }\n        }\n        return processed;\n    };\n    // Handle training job creation or update\n    const handleStartTraining = async ()=>{\n        if (!selectedConfigId || !trainingPrompts.trim()) {\n            setError('Please select an API configuration and provide training prompts.');\n            return;\n        }\n        // Prevent multiple simultaneous training operations\n        if (isLoading) {\n            console.warn('[Training] Operation already in progress, ignoring duplicate request');\n            return;\n        }\n        setIsLoading(true);\n        setError(null);\n        setSuccessMessage(null);\n        try {\n            var _customConfigs_find;\n            // Process training prompts\n            const processedPrompts = processTrainingPrompts(trainingPrompts);\n            // Generate a meaningful name based on content\n            const configName = ((_customConfigs_find = customConfigs.find((c)=>c.id === selectedConfigId)) === null || _customConfigs_find === void 0 ? void 0 : _customConfigs_find.name) || 'Unknown Config';\n            // Prepare training job data\n            const trainingJobData = {\n                custom_api_config_id: selectedConfigId,\n                name: \"\".concat(configName, \" Training - \").concat(new Date().toLocaleDateString()),\n                description: \"Training job for \".concat(configName, \" with \").concat(processedPrompts.examples.length, \" examples\"),\n                training_data: {\n                    processed_prompts: processedPrompts,\n                    raw_prompts: trainingPrompts.trim(),\n                    last_prompt_update: new Date().toISOString()\n                },\n                parameters: {\n                    training_type: 'prompt_engineering',\n                    created_via: 'training_page',\n                    version: '1.0'\n                }\n            };\n            // Use UPSERT to handle both create and update scenarios\n            const response = await fetch('/api/training/jobs/upsert', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(trainingJobData)\n            });\n            if (!response.ok) {\n                const errorText = await response.text();\n                console.error('[Training] Failed to upsert training job:', errorText);\n                throw new Error(\"Failed to save training job: \".concat(response.status, \" \").concat(errorText));\n            }\n            const result = await response.json();\n            const isUpdate = result.operation === 'updated';\n            console.log(\"[Training] Successfully \".concat(isUpdate ? 'updated' : 'created', \" training job:\"), result.id);\n            // Show success message based on operation type\n            const operationText = isUpdate ? 'updated' : 'enhanced';\n            const operationEmoji = isUpdate ? '🔄' : '🎉';\n            const successMessage = \"\".concat(operationEmoji, \" Prompt Engineering \").concat(isUpdate ? 'updated' : 'completed', \" successfully!\\n\\n\") + 'Your \"'.concat(configName, '\" configuration has been ').concat(operationText, \" with:\\n\") + \"• \".concat(processedPrompts.examples.length, \" training examples\\n\") + \"• Custom system instructions and behavior guidelines\\n\" + \"\\n✨ All future chats using this configuration will automatically:\\n\" + \"• Follow your training examples\\n\" + \"• Apply your behavior guidelines\\n\" + \"• Maintain consistent personality and responses\\n\\n\" + \"\\uD83D\\uDE80 Try it now in the Playground to see your \".concat(isUpdate ? 'updated' : 'enhanced', \" model in action!\\n\\n\") + \"\\uD83D\\uDCA1 Your training prompts remain here so you can modify them anytime.\";\n            setSuccessMessage(successMessage);\n        } catch (err) {\n            console.error('Error in training operation:', err);\n            setError(\"Failed to create prompt engineering: \".concat(err.message));\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-[#faf8f5] p-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-6xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-4xl font-bold text-gray-900\",\n                                    children: \"AI Training & Enhancement\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                    lineNumber: 215,\n                                    columnNumber: 13\n                                }, this),\n                                subscriptionStatus && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TierEnforcement__WEBPACK_IMPORTED_MODULE_3__.TierBadge, {\n                                    tier: subscriptionStatus.tier,\n                                    size: \"lg\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                    lineNumber: 217,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                            lineNumber: 214,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg text-gray-600 max-w-3xl\",\n                            children: \"Enhance your AI models with custom prompts and knowledge documents. Upload your proprietary content to create domain-specific AI assistants.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                            lineNumber: 220,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                    lineNumber: 213,\n                    columnNumber: 9\n                }, this),\n                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6 bg-red-50 border border-red-200 rounded-xl p-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-5 h-5 text-red-600\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                    lineNumber: 230,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                lineNumber: 229,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-red-800 text-sm font-medium\",\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                lineNumber: 232,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                        lineNumber: 228,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                    lineNumber: 227,\n                    columnNumber: 11\n                }, this),\n                successMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6 bg-green-50 border border-green-200 rounded-xl p-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-5 h-5 text-green-600\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M5 13l4 4L19 7\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                    lineNumber: 241,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                lineNumber: 240,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-green-800 text-sm font-medium\",\n                                children: successMessage\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                lineNumber: 243,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                        lineNumber: 239,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                    lineNumber: 238,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TierEnforcement__WEBPACK_IMPORTED_MODULE_3__.TierGuard, {\n                    feature: \"knowledge_base\",\n                    customMessage: \"Knowledge base document upload is available starting with the Professional plan. Upload documents to enhance your AI with proprietary knowledge and create domain-specific assistants.\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-2xl shadow-lg p-8 mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3 mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-5 h-5 text-orange-600\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                lineNumber: 257,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                            lineNumber: 256,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                        lineNumber: 255,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-2xl font-bold text-gray-900\",\n                                                children: \"Knowledge Documents\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                lineNumber: 261,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"Upload documents to enhance your AI with proprietary knowledge\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                lineNumber: 262,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                        lineNumber: 260,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                lineNumber: 254,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DocumentUpload__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                configId: selectedConfigId,\n                                onDocumentUploaded: ()=>{\n                                    // Optionally refresh something or show a message\n                                    console.log('Document uploaded successfully');\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                lineNumber: 266,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                        lineNumber: 253,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                    lineNumber: 249,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-2xl shadow-lg p-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3 mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-5 h-5 text-blue-600\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                            lineNumber: 281,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                        lineNumber: 280,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                    lineNumber: 279,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-2xl font-bold text-gray-900\",\n                                            children: \"Custom Prompts\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                            lineNumber: 285,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"Define behavior, examples, and instructions for your AI\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                            lineNumber: 286,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                    lineNumber: 284,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                            lineNumber: 278,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"configSelect\",\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Select API Configuration\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                            lineNumber: 293,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            id: \"configSelect\",\n                                            value: selectedConfigId,\n                                            onChange: (e)=>setSelectedConfigId(e.target.value),\n                                            className: \"w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors bg-white text-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"\",\n                                                    children: \"Choose which model to train...\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                    lineNumber: 302,\n                                                    columnNumber: 17\n                                                }, this),\n                                                customConfigs.map((config)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: config.id,\n                                                        children: config.name\n                                                    }, config.id, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                        lineNumber: 304,\n                                                        columnNumber: 19\n                                                    }, this))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                            lineNumber: 296,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                    lineNumber: 292,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"trainingPrompts\",\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Custom Prompts & Instructions\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                            lineNumber: 313,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            id: \"trainingPrompts\",\n                                            value: trainingPrompts,\n                                            onChange: (e)=>setTrainingPrompts(e.target.value),\n                                            placeholder: \"Enter your training prompts using these formats:\\n\\nSYSTEM: You are a helpful customer service agent for our company\\nBEHAVIOR: Always be polite and offer solutions\\n\\nUser asks about returns → I'd be happy to help with your return! Let me check our policy for you.\\nCustomer is frustrated → I understand your frustration. Let me see how I can resolve this for you.\\n\\nGeneral instructions can be written as regular text.\",\n                                            rows: 12,\n                                            className: \"w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors bg-white text-sm resize-none font-mono\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                            lineNumber: 316,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-3 bg-blue-50 border border-blue-200 rounded-lg p-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"text-sm font-medium text-blue-900 mb-2\",\n                                                    children: \"Training Format Guide:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                    lineNumber: 333,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"text-xs text-blue-800 space-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"SYSTEM:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                                    lineNumber: 335,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \" Core instructions for the AI's role and personality\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                            lineNumber: 335,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"BEHAVIOR:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                                    lineNumber: 336,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \" Guidelines for how the AI should behave\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                            lineNumber: 336,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"Examples:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                                    lineNumber: 337,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                ' Use \"User input → Expected response\" format'\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                            lineNumber: 337,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"General:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                                    lineNumber: 338,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \" Any other instructions written as normal text\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                            lineNumber: 338,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                    lineNumber: 334,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                            lineNumber: 332,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                    lineNumber: 312,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center pt-6 border-t border-gray-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                className: \"btn-secondary\",\n                                                onClick: ()=>{\n                                                    if (confirm('Clear all training prompts?')) {\n                                                        setTrainingPrompts('');\n                                                    }\n                                                },\n                                                children: \"Clear Form\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                lineNumber: 348,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                            lineNumber: 347,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: handleStartTraining,\n                                            disabled: !selectedConfigId || !trainingPrompts.trim() || isLoading,\n                                            className: \"btn-primary disabled:opacity-50 disabled:cursor-not-allowed\",\n                                            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                        lineNumber: 369,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"Processing Prompts...\"\n                                                ]\n                                            }, void 0, true) : 'Save Prompts'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                            lineNumber: 361,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                    lineNumber: 346,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                            lineNumber: 290,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                    lineNumber: 277,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n            lineNumber: 211,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n        lineNumber: 210,\n        columnNumber: 5\n    }, this);\n}\n_s(TrainingPage, \"AnIIwIukYck8ECa5wQhNDO+WsdY=\", false, function() {\n    return [\n        _hooks_useSubscription__WEBPACK_IMPORTED_MODULE_4__.useSubscription\n    ];\n});\n_c = TrainingPage;\nvar _c;\n$RefreshReg$(_c, \"TrainingPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvdHJhaW5pbmcvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBRTRDO0FBR2E7QUFDVztBQUNWO0FBRzNDLFNBQVNNOztJQUN0QixvQkFBb0I7SUFDcEIsTUFBTSxFQUFFQyxrQkFBa0IsRUFBRSxHQUFHRix1RUFBZUE7SUFFOUMsbUJBQW1CO0lBQ25CLE1BQU0sQ0FBQ0csZUFBZUMsaUJBQWlCLEdBQUdULCtDQUFRQSxDQUFvQixFQUFFO0lBQ3hFLE1BQU0sQ0FBQ1Usa0JBQWtCQyxvQkFBb0IsR0FBR1gsK0NBQVFBLENBQVM7SUFDakUsTUFBTSxDQUFDWSxjQUFjQyxnQkFBZ0IsR0FBR2IsK0NBQVFBLENBQWdCLEVBQUU7SUFDbEUsTUFBTSxDQUFDYyxXQUFXQyxhQUFhLEdBQUdmLCtDQUFRQSxDQUFVO0lBQ3BELE1BQU0sQ0FBQ2dCLE9BQU9DLFNBQVMsR0FBR2pCLCtDQUFRQSxDQUFnQjtJQUNsRCxNQUFNLENBQUNrQixnQkFBZ0JDLGtCQUFrQixHQUFHbkIsK0NBQVFBLENBQWdCO0lBRXBFLGdDQUFnQztJQUNoQyxNQUFNLENBQUNvQixpQkFBaUJDLG1CQUFtQixHQUFHckIsK0NBQVFBLENBQVM7SUFJL0Qsa0RBQWtEO0lBQ2xELE1BQU1zQiwyQkFBMkIsT0FBT0M7UUFDdEMsSUFBSSxDQUFDQSxVQUFVO1FBRWYsSUFBSTtZQUNGLHFCQUFxQjtZQUNyQixNQUFNQyxlQUFlLE1BQU1DLE1BQU0sMkNBQW9ELE9BQVRGO1lBQzVFLElBQUlDLGFBQWFFLEVBQUUsRUFBRTtnQkFDbkIsTUFBTUMsT0FBTyxNQUFNSCxhQUFhSSxJQUFJO2dCQUNwQyxJQUFJRCxLQUFLRSxNQUFNLEdBQUcsR0FBRzt3QkFJZkM7b0JBSEosTUFBTUEsWUFBWUgsSUFBSSxDQUFDLEVBQUU7b0JBRXpCLHdCQUF3QjtvQkFDeEIsS0FBSUcsMkJBQUFBLFVBQVVDLGFBQWEsY0FBdkJELCtDQUFBQSx5QkFBeUJFLFdBQVcsRUFBRTt3QkFDeENYLG1CQUFtQlMsVUFBVUMsYUFBYSxDQUFDQyxXQUFXO29CQUN4RDtnQkFHRjtZQUNGO1FBQ0YsRUFBRSxPQUFPQyxLQUFVO1lBQ2pCQyxRQUFRQyxJQUFJLENBQUMsMENBQTBDRjtRQUN6RDtJQUNGO0lBRUEsOENBQThDO0lBQzlDaEMsZ0RBQVNBO2tDQUFDO1lBQ1IsTUFBTW1DO3VEQUFlO29CQUNuQixJQUFJO3dCQUNGLE1BQU1DLFdBQVcsTUFBTVosTUFBTTt3QkFDN0IsSUFBSSxDQUFDWSxTQUFTWCxFQUFFLEVBQUU7NEJBQ2hCLE1BQU0sSUFBSVksTUFBTTt3QkFDbEI7d0JBQ0EsTUFBTUMsT0FBMEIsTUFBTUYsU0FBU1QsSUFBSTt3QkFDbkRuQixpQkFBaUI4Qjt3QkFDakIsSUFBSUEsS0FBS1YsTUFBTSxHQUFHLEdBQUc7NEJBQ25CbEIsb0JBQW9CNEIsSUFBSSxDQUFDLEVBQUUsQ0FBQ0MsRUFBRTs0QkFDOUJsQix5QkFBeUJpQixJQUFJLENBQUMsRUFBRSxDQUFDQyxFQUFFO3dCQUNyQztvQkFDRixFQUFFLE9BQU9QLEtBQVU7d0JBQ2pCaEIsU0FBUyxrQ0FBOEMsT0FBWmdCLElBQUlRLE9BQU87b0JBQ3hEO2dCQUNGOztZQUNBTDtRQUNGO2lDQUFHLEVBQUU7SUFFTCxnREFBZ0Q7SUFDaERuQyxnREFBU0E7a0NBQUM7WUFDUixJQUFJUyxrQkFBa0I7Z0JBQ3BCWSx5QkFBeUJaO1lBQzNCO1FBQ0Y7aUNBQUc7UUFBQ0E7S0FBaUI7SUFVckIsa0RBQWtEO0lBQ2xELE1BQU1nQyx5QkFBeUIsQ0FBQ0M7UUFDOUIsTUFBTUMsWUFBWTtZQUNoQkMscUJBQXFCO1lBQ3JCQyxVQUFVLEVBQUU7WUFDWkMscUJBQXFCO1lBQ3JCQyxzQkFBc0I7UUFDeEI7UUFFQSxNQUFNQyxRQUFRTixRQUFRTyxLQUFLLENBQUMsTUFBTUMsTUFBTSxDQUFDQyxDQUFBQSxPQUFRQSxLQUFLQyxJQUFJO1FBRTFELEtBQUssTUFBTUQsUUFBUUgsTUFBTztZQUN4QixNQUFNSyxjQUFjRixLQUFLQyxJQUFJO1lBRTdCLElBQUlDLFlBQVlDLFVBQVUsQ0FBQyxZQUFZO2dCQUNyQ1gsVUFBVUMsbUJBQW1CLElBQUlTLFlBQVlFLE9BQU8sQ0FBQyxXQUFXLElBQUlILElBQUksS0FBSztZQUMvRSxPQUFPLElBQUlDLFlBQVlDLFVBQVUsQ0FBQyxjQUFjO2dCQUM5Q1gsVUFBVUcsbUJBQW1CLElBQUlPLFlBQVlFLE9BQU8sQ0FBQyxhQUFhLElBQUlILElBQUksS0FBSztZQUNqRixPQUFPLElBQUlDLFlBQVlHLFFBQVEsQ0FBQyxRQUFRSCxZQUFZRyxRQUFRLENBQUMsT0FBTztnQkFDbEUsa0RBQWtEO2dCQUNsRCxNQUFNQyxZQUFZSixZQUFZRyxRQUFRLENBQUMsT0FBTyxNQUFNO2dCQUNwRCxNQUFNRSxRQUFRTCxZQUFZSixLQUFLLENBQUNRO2dCQUNoQyxJQUFJQyxNQUFNOUIsTUFBTSxJQUFJLEdBQUc7b0JBQ3JCLE1BQU0rQixRQUFRRCxLQUFLLENBQUMsRUFBRSxDQUFDTixJQUFJO29CQUMzQixNQUFNUSxTQUFTRixNQUFNRyxLQUFLLENBQUMsR0FBR0MsSUFBSSxDQUFDTCxXQUFXTCxJQUFJO29CQUNsRFQsVUFBVUUsUUFBUSxDQUFDa0IsSUFBSSxDQUFDO3dCQUFFSjt3QkFBT0M7b0JBQU87Z0JBQzFDO1lBQ0YsT0FBTyxJQUFJUCxZQUFZekIsTUFBTSxHQUFHLEdBQUc7Z0JBQ2pDLHNCQUFzQjtnQkFDdEJlLFVBQVVJLG9CQUFvQixJQUFJTSxjQUFjO1lBQ2xEO1FBQ0Y7UUFFQSxPQUFPVjtJQUNUO0lBRUEseUNBQXlDO0lBQ3pDLE1BQU1xQixzQkFBc0I7UUFDMUIsSUFBSSxDQUFDdkQsb0JBQW9CLENBQUNVLGdCQUFnQmlDLElBQUksSUFBSTtZQUNoRHBDLFNBQVM7WUFDVDtRQUNGO1FBRUEsb0RBQW9EO1FBQ3BELElBQUlILFdBQVc7WUFDYm9CLFFBQVFDLElBQUksQ0FBQztZQUNiO1FBQ0Y7UUFFQXBCLGFBQWE7UUFDYkUsU0FBUztRQUNURSxrQkFBa0I7UUFFbEIsSUFBSTtnQkFLaUJYO1lBSm5CLDJCQUEyQjtZQUMzQixNQUFNMEQsbUJBQW1CeEIsdUJBQXVCdEI7WUFFaEQsOENBQThDO1lBQzlDLE1BQU0rQyxhQUFhM0QsRUFBQUEsc0JBQUFBLGNBQWM0RCxJQUFJLENBQUNDLENBQUFBLElBQUtBLEVBQUU3QixFQUFFLEtBQUs5QiwrQkFBakNGLDBDQUFBQSxvQkFBb0Q4RCxJQUFJLEtBQUk7WUFFL0UsNEJBQTRCO1lBQzVCLE1BQU1DLGtCQUFrQjtnQkFDdEJDLHNCQUFzQjlEO2dCQUN0QjRELE1BQU0sR0FBNEIsT0FBekJILFlBQVcsZ0JBQThDLE9BQWhDLElBQUlNLE9BQU9DLGtCQUFrQjtnQkFDL0RDLGFBQWEsb0JBQXVDVCxPQUFuQkMsWUFBVyxVQUF5QyxPQUFqQ0QsaUJBQWlCcEIsUUFBUSxDQUFDakIsTUFBTSxFQUFDO2dCQUNyRkUsZUFBZTtvQkFDYjZDLG1CQUFtQlY7b0JBQ25CbEMsYUFBYVosZ0JBQWdCaUMsSUFBSTtvQkFDakN3QixvQkFBb0IsSUFBSUosT0FBT0ssV0FBVztnQkFDNUM7Z0JBQ0FDLFlBQVk7b0JBQ1ZDLGVBQWU7b0JBQ2ZDLGFBQWE7b0JBQ2JDLFNBQVM7Z0JBQ1g7WUFDRjtZQUVBLHdEQUF3RDtZQUN4RCxNQUFNN0MsV0FBVyxNQUFNWixNQUFNLDZCQUE2QjtnQkFDeEQwRCxRQUFRO2dCQUNSQyxTQUFTO29CQUFFLGdCQUFnQjtnQkFBbUI7Z0JBQzlDQyxNQUFNQyxLQUFLQyxTQUFTLENBQUNoQjtZQUN2QjtZQUVBLElBQUksQ0FBQ2xDLFNBQVNYLEVBQUUsRUFBRTtnQkFDaEIsTUFBTThELFlBQVksTUFBTW5ELFNBQVNvRCxJQUFJO2dCQUNyQ3ZELFFBQVFsQixLQUFLLENBQUMsNkNBQTZDd0U7Z0JBQzNELE1BQU0sSUFBSWxELE1BQU0sZ0NBQW1Ea0QsT0FBbkJuRCxTQUFTcUQsTUFBTSxFQUFDLEtBQWEsT0FBVkY7WUFDckU7WUFFQSxNQUFNRyxTQUFTLE1BQU10RCxTQUFTVCxJQUFJO1lBQ2xDLE1BQU1nRSxXQUFXRCxPQUFPRSxTQUFTLEtBQUs7WUFFdEMzRCxRQUFRNEQsR0FBRyxDQUFDLDJCQUE0RCxPQUFqQ0YsV0FBVyxZQUFZLFdBQVUsbUJBQWlCRCxPQUFPbkQsRUFBRTtZQUVsRywrQ0FBK0M7WUFDL0MsTUFBTXVELGdCQUFnQkgsV0FBVyxZQUFZO1lBQzdDLE1BQU1JLGlCQUFpQkosV0FBVyxPQUFPO1lBRXpDLE1BQU0xRSxpQkFBaUIsR0FBd0MwRSxPQUFyQ0ksZ0JBQWUsd0JBQXlELE9BQW5DSixXQUFXLFlBQVksYUFBWSx3QkFDaEcsU0FBK0NHLE9BQXRDNUIsWUFBVyw2QkFBeUMsT0FBZDRCLGVBQWMsY0FDN0QsS0FBc0MsT0FBakM3QixpQkFBaUJwQixRQUFRLENBQUNqQixNQUFNLEVBQUMsMEJBQ3JDLDJEQUNBLHdFQUNBLHNDQUNBLHVDQUNBLHdEQUNELHlEQUFpRixPQUFsQytELFdBQVcsWUFBWSxZQUFXLDJCQUNoRjtZQUVIekUsa0JBQWtCRDtRQUVwQixFQUFFLE9BQU9lLEtBQVU7WUFDakJDLFFBQVFsQixLQUFLLENBQUMsZ0NBQWdDaUI7WUFDOUNoQixTQUFTLHdDQUFvRCxPQUFaZ0IsSUFBSVEsT0FBTztRQUM5RCxTQUFVO1lBQ1IxQixhQUFhO1FBQ2Y7SUFDRjtJQUVBLHFCQUNFLDhEQUFDa0Y7UUFBSUMsV0FBVTtrQkFDYiw0RUFBQ0Q7WUFBSUMsV0FBVTs7OEJBRWIsOERBQUNEO29CQUFJQyxXQUFVOztzQ0FDYiw4REFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDQztvQ0FBR0QsV0FBVTs4Q0FBbUM7Ozs7OztnQ0FDaEQzRixvQ0FDQyw4REFBQ0gsa0VBQVNBO29DQUFDZ0csTUFBTTdGLG1CQUFtQjZGLElBQUk7b0NBQUVDLE1BQUs7Ozs7Ozs7Ozs7OztzQ0FHbkQsOERBQUNDOzRCQUFFSixXQUFVO3NDQUFrQzs7Ozs7Ozs7Ozs7O2dCQU1oRGxGLHVCQUNDLDhEQUFDaUY7b0JBQUlDLFdBQVU7OEJBQ2IsNEVBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0s7Z0NBQUlMLFdBQVU7Z0NBQXVCTSxNQUFLO2dDQUFPQyxRQUFPO2dDQUFlQyxTQUFROzBDQUM5RSw0RUFBQ0M7b0NBQUtDLGVBQWM7b0NBQVFDLGdCQUFlO29DQUFRQyxhQUFhO29DQUFHQyxHQUFFOzs7Ozs7Ozs7OzswQ0FFdkUsOERBQUNUO2dDQUFFSixXQUFVOzBDQUFvQ2xGOzs7Ozs7Ozs7Ozs7Ozs7OztnQkFLdERFLGdDQUNDLDhEQUFDK0U7b0JBQUlDLFdBQVU7OEJBQ2IsNEVBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0s7Z0NBQUlMLFdBQVU7Z0NBQXlCTSxNQUFLO2dDQUFPQyxRQUFPO2dDQUFlQyxTQUFROzBDQUNoRiw0RUFBQ0M7b0NBQUtDLGVBQWM7b0NBQVFDLGdCQUFlO29DQUFRQyxhQUFhO29DQUFHQyxHQUFFOzs7Ozs7Ozs7OzswQ0FFdkUsOERBQUNUO2dDQUFFSixXQUFVOzBDQUFzQ2hGOzs7Ozs7Ozs7Ozs7Ozs7Ozs4QkFNekQsOERBQUNmLGtFQUFTQTtvQkFDUjZHLFNBQVE7b0JBQ1JDLGVBQWM7OEJBRWQsNEVBQUNoQjt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ0Q7d0NBQUlDLFdBQVU7a0RBQ2IsNEVBQUNLOzRDQUFJTCxXQUFVOzRDQUEwQk0sTUFBSzs0Q0FBT0MsUUFBTzs0Q0FBZUMsU0FBUTtzREFDakYsNEVBQUNDO2dEQUFLQyxlQUFjO2dEQUFRQyxnQkFBZTtnREFBUUMsYUFBYTtnREFBR0MsR0FBRTs7Ozs7Ozs7Ozs7Ozs7OztrREFHekUsOERBQUNkOzswREFDQyw4REFBQ2lCO2dEQUFHaEIsV0FBVTswREFBbUM7Ozs7OzswREFDakQsOERBQUNJO2dEQUFFSixXQUFVOzBEQUF3Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBDQUl6Qyw4REFBQ2hHLGtFQUFjQTtnQ0FDYnFCLFVBQVViO2dDQUNWeUcsb0JBQW9CO29DQUNsQixpREFBaUQ7b0NBQ2pEakYsUUFBUTRELEdBQUcsQ0FBQztnQ0FDZDs7Ozs7Ozs7Ozs7Ozs7Ozs7OEJBTU4sOERBQUNHO29CQUFJQyxXQUFVOztzQ0FDYiw4REFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDRDtvQ0FBSUMsV0FBVTs4Q0FDYiw0RUFBQ0s7d0NBQUlMLFdBQVU7d0NBQXdCTSxNQUFLO3dDQUFPQyxRQUFPO3dDQUFlQyxTQUFRO2tEQUMvRSw0RUFBQ0M7NENBQUtDLGVBQWM7NENBQVFDLGdCQUFlOzRDQUFRQyxhQUFhOzRDQUFHQyxHQUFFOzs7Ozs7Ozs7Ozs7Ozs7OzhDQUd6RSw4REFBQ2Q7O3NEQUNDLDhEQUFDaUI7NENBQUdoQixXQUFVO3NEQUFtQzs7Ozs7O3NEQUNqRCw4REFBQ0k7NENBQUVKLFdBQVU7c0RBQXdCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBSXpDLDhEQUFDRDs0QkFBSUMsV0FBVTs7OENBRWIsOERBQUNEOztzREFDQyw4REFBQ21COzRDQUFNQyxTQUFROzRDQUFlbkIsV0FBVTtzREFBK0M7Ozs7OztzREFHdkYsOERBQUNvQjs0Q0FDQzlFLElBQUc7NENBQ0grRSxPQUFPN0c7NENBQ1A4RyxVQUFVLENBQUNDLElBQU05RyxvQkFBb0I4RyxFQUFFQyxNQUFNLENBQUNILEtBQUs7NENBQ25EckIsV0FBVTs7OERBRVYsOERBQUN5QjtvREFBT0osT0FBTTs4REFBRzs7Ozs7O2dEQUNoQi9HLGNBQWNvSCxHQUFHLENBQUMsQ0FBQ0MsdUJBQ2xCLDhEQUFDRjt3REFBdUJKLE9BQU9NLE9BQU9yRixFQUFFO2tFQUNyQ3FGLE9BQU92RCxJQUFJO3VEQUREdUQsT0FBT3JGLEVBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7OzhDQVE1Qiw4REFBQ3lEOztzREFDQyw4REFBQ21COzRDQUFNQyxTQUFROzRDQUFrQm5CLFdBQVU7c0RBQStDOzs7Ozs7c0RBRzFGLDhEQUFDNEI7NENBQ0N0RixJQUFHOzRDQUNIK0UsT0FBT25HOzRDQUNQb0csVUFBVSxDQUFDQyxJQUFNcEcsbUJBQW1Cb0csRUFBRUMsTUFBTSxDQUFDSCxLQUFLOzRDQUNsRFEsYUFBYzs0Q0FTZEMsTUFBTTs0Q0FDTjlCLFdBQVU7Ozs7OztzREFFWiw4REFBQ0Q7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDK0I7b0RBQUcvQixXQUFVOzhEQUF5Qzs7Ozs7OzhEQUN2RCw4REFBQ2dDO29EQUFHaEMsV0FBVTs7c0VBQ1osOERBQUNpQzs7OEVBQUcsOERBQUNDOzhFQUFPOzs7Ozs7Z0VBQWdCOzs7Ozs7O3NFQUM1Qiw4REFBQ0Q7OzhFQUFHLDhEQUFDQzs4RUFBTzs7Ozs7O2dFQUFrQjs7Ozs7OztzRUFDOUIsOERBQUNEOzs4RUFBRyw4REFBQ0M7OEVBQU87Ozs7OztnRUFBa0I7Ozs7Ozs7c0VBQzlCLDhEQUFDRDs7OEVBQUcsOERBQUNDOzhFQUFPOzs7Ozs7Z0VBQWlCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhDQVFuQyw4REFBQ25DO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ0Q7NENBQUlDLFdBQVU7c0RBQ2IsNEVBQUNtQztnREFDQ0MsTUFBSztnREFDTHBDLFdBQVU7Z0RBQ1ZxQyxTQUFTO29EQUNQLElBQUlDLFFBQVEsZ0NBQWdDO3dEQUMxQ25ILG1CQUFtQjtvREFDckI7Z0RBQ0Y7MERBQ0Q7Ozs7Ozs7Ozs7O3NEQUtILDhEQUFDZ0g7NENBQ0NDLE1BQUs7NENBQ0xDLFNBQVN0RTs0Q0FDVHdFLFVBQVUsQ0FBQy9ILG9CQUFvQixDQUFDVSxnQkFBZ0JpQyxJQUFJLE1BQU12Qzs0Q0FDMURvRixXQUFVO3NEQUVUcEYsMEJBQ0M7O2tFQUNFLDhEQUFDbUY7d0RBQUlDLFdBQVU7Ozs7OztvREFBdUU7OytEQUl4Rjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFXbEI7R0FyWHdCNUY7O1FBRVNELG1FQUFlQTs7O0tBRnhCQyIsInNvdXJjZXMiOlsiQzpcXFJvS2V5IEFwcFxccm9rZXktYXBwXFxzcmNcXGFwcFxcdHJhaW5pbmdcXHBhZ2UudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcclxuXHJcbmltcG9ydCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCc7XHJcbmltcG9ydCB7IHR5cGUgQ3VzdG9tQXBpQ29uZmlnIH0gZnJvbSAnQC90eXBlcy9jdXN0b21BcGlDb25maWdzJztcclxuaW1wb3J0IHsgdHlwZSBUcmFpbmluZ0pvYiwgdHlwZSBUcmFpbmluZ1Byb21wdCB9IGZyb20gJ0AvdHlwZXMvdHJhaW5pbmcnO1xyXG5pbXBvcnQgRG9jdW1lbnRVcGxvYWQgZnJvbSAnQC9jb21wb25lbnRzL0RvY3VtZW50VXBsb2FkJztcclxuaW1wb3J0IHsgVGllckd1YXJkLCBUaWVyQmFkZ2UgfSBmcm9tICdAL2NvbXBvbmVudHMvVGllckVuZm9yY2VtZW50JztcclxuaW1wb3J0IHsgdXNlU3Vic2NyaXB0aW9uIH0gZnJvbSAnQC9ob29rcy91c2VTdWJzY3JpcHRpb24nO1xyXG5cclxuXHJcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFRyYWluaW5nUGFnZSgpIHtcclxuICAvLyBTdWJzY3JpcHRpb24gaG9va1xyXG4gIGNvbnN0IHsgc3Vic2NyaXB0aW9uU3RhdHVzIH0gPSB1c2VTdWJzY3JpcHRpb24oKTtcclxuXHJcbiAgLy8gU3RhdGUgbWFuYWdlbWVudFxyXG4gIGNvbnN0IFtjdXN0b21Db25maWdzLCBzZXRDdXN0b21Db25maWdzXSA9IHVzZVN0YXRlPEN1c3RvbUFwaUNvbmZpZ1tdPihbXSk7XHJcbiAgY29uc3QgW3NlbGVjdGVkQ29uZmlnSWQsIHNldFNlbGVjdGVkQ29uZmlnSWRdID0gdXNlU3RhdGU8c3RyaW5nPignJyk7XHJcbiAgY29uc3QgW3RyYWluaW5nSm9icywgc2V0VHJhaW5pbmdKb2JzXSA9IHVzZVN0YXRlPFRyYWluaW5nSm9iW10+KFtdKTtcclxuICBjb25zdCBbaXNMb2FkaW5nLCBzZXRJc0xvYWRpbmddID0gdXNlU3RhdGU8Ym9vbGVhbj4oZmFsc2UpO1xyXG4gIGNvbnN0IFtlcnJvciwgc2V0RXJyb3JdID0gdXNlU3RhdGU8c3RyaW5nIHwgbnVsbD4obnVsbCk7XHJcbiAgY29uc3QgW3N1Y2Nlc3NNZXNzYWdlLCBzZXRTdWNjZXNzTWVzc2FnZV0gPSB1c2VTdGF0ZTxzdHJpbmcgfCBudWxsPihudWxsKTtcclxuXHJcbiAgLy8gUHJvbXB0IGVuZ2luZWVyaW5nIGZvcm0gc3RhdGVcclxuICBjb25zdCBbdHJhaW5pbmdQcm9tcHRzLCBzZXRUcmFpbmluZ1Byb21wdHNdID0gdXNlU3RhdGU8c3RyaW5nPignJyk7XHJcblxyXG5cclxuXHJcbiAgLy8gTG9hZCBleGlzdGluZyB0cmFpbmluZyBkYXRhIGZvciBhIGNvbmZpZ3VyYXRpb25cclxuICBjb25zdCBsb2FkRXhpc3RpbmdUcmFpbmluZ0RhdGEgPSBhc3luYyAoY29uZmlnSWQ6IHN0cmluZykgPT4ge1xyXG4gICAgaWYgKCFjb25maWdJZCkgcmV0dXJuO1xyXG5cclxuICAgIHRyeSB7XHJcbiAgICAgIC8vIExvYWQgdHJhaW5pbmcgam9ic1xyXG4gICAgICBjb25zdCBqb2JzUmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgL2FwaS90cmFpbmluZy9qb2JzP2N1c3RvbV9hcGlfY29uZmlnX2lkPSR7Y29uZmlnSWR9YCk7XHJcbiAgICAgIGlmIChqb2JzUmVzcG9uc2Uub2spIHtcclxuICAgICAgICBjb25zdCBqb2JzID0gYXdhaXQgam9ic1Jlc3BvbnNlLmpzb24oKTtcclxuICAgICAgICBpZiAoam9icy5sZW5ndGggPiAwKSB7XHJcbiAgICAgICAgICBjb25zdCBsYXRlc3RKb2IgPSBqb2JzWzBdO1xyXG5cclxuICAgICAgICAgIC8vIExvYWQgdHJhaW5pbmcgcHJvbXB0c1xyXG4gICAgICAgICAgaWYgKGxhdGVzdEpvYi50cmFpbmluZ19kYXRhPy5yYXdfcHJvbXB0cykge1xyXG4gICAgICAgICAgICBzZXRUcmFpbmluZ1Byb21wdHMobGF0ZXN0Sm9iLnRyYWluaW5nX2RhdGEucmF3X3Byb21wdHMpO1xyXG4gICAgICAgICAgfVxyXG5cclxuXHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcbiAgICB9IGNhdGNoIChlcnI6IGFueSkge1xyXG4gICAgICBjb25zb2xlLndhcm4oJ0ZhaWxlZCB0byBsb2FkIGV4aXN0aW5nIHRyYWluaW5nIGRhdGE6JywgZXJyKTtcclxuICAgIH1cclxuICB9O1xyXG5cclxuICAvLyBGZXRjaCBjdXN0b20gQVBJIGNvbmZpZ3Mgb24gY29tcG9uZW50IG1vdW50XHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIGNvbnN0IGZldGNoQ29uZmlncyA9IGFzeW5jICgpID0+IHtcclxuICAgICAgdHJ5IHtcclxuICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKCcvYXBpL2N1c3RvbS1jb25maWdzJyk7XHJcbiAgICAgICAgaWYgKCFyZXNwb25zZS5vaykge1xyXG4gICAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdGYWlsZWQgdG8gZmV0Y2ggY29uZmlndXJhdGlvbnMnKTtcclxuICAgICAgICB9XHJcbiAgICAgICAgY29uc3QgZGF0YTogQ3VzdG9tQXBpQ29uZmlnW10gPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XHJcbiAgICAgICAgc2V0Q3VzdG9tQ29uZmlncyhkYXRhKTtcclxuICAgICAgICBpZiAoZGF0YS5sZW5ndGggPiAwKSB7XHJcbiAgICAgICAgICBzZXRTZWxlY3RlZENvbmZpZ0lkKGRhdGFbMF0uaWQpO1xyXG4gICAgICAgICAgbG9hZEV4aXN0aW5nVHJhaW5pbmdEYXRhKGRhdGFbMF0uaWQpO1xyXG4gICAgICAgIH1cclxuICAgICAgfSBjYXRjaCAoZXJyOiBhbnkpIHtcclxuICAgICAgICBzZXRFcnJvcihgRmFpbGVkIHRvIGxvYWQgY29uZmlndXJhdGlvbnM6ICR7ZXJyLm1lc3NhZ2V9YCk7XHJcbiAgICAgIH1cclxuICAgIH07XHJcbiAgICBmZXRjaENvbmZpZ3MoKTtcclxuICB9LCBbXSk7XHJcblxyXG4gIC8vIExvYWQgdHJhaW5pbmcgZGF0YSB3aGVuIGNvbmZpZ3VyYXRpb24gY2hhbmdlc1xyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBpZiAoc2VsZWN0ZWRDb25maWdJZCkge1xyXG4gICAgICBsb2FkRXhpc3RpbmdUcmFpbmluZ0RhdGEoc2VsZWN0ZWRDb25maWdJZCk7XHJcbiAgICB9XHJcbiAgfSwgW3NlbGVjdGVkQ29uZmlnSWRdKTtcclxuXHJcblxyXG5cclxuXHJcblxyXG5cclxuXHJcblxyXG5cclxuICAvLyBQcm9jZXNzIHRyYWluaW5nIHByb21wdHMgaW50byBzdHJ1Y3R1cmVkIGZvcm1hdFxyXG4gIGNvbnN0IHByb2Nlc3NUcmFpbmluZ1Byb21wdHMgPSAocHJvbXB0czogc3RyaW5nKSA9PiB7XHJcbiAgICBjb25zdCBwcm9jZXNzZWQgPSB7XHJcbiAgICAgIHN5c3RlbV9pbnN0cnVjdGlvbnM6ICcnLFxyXG4gICAgICBleGFtcGxlczogW10gYXMgQXJyYXk8e2lucHV0OiBzdHJpbmcsIG91dHB1dDogc3RyaW5nfT4sXHJcbiAgICAgIGJlaGF2aW9yX2d1aWRlbGluZXM6ICcnLFxyXG4gICAgICBnZW5lcmFsX2luc3RydWN0aW9uczogJydcclxuICAgIH07XHJcblxyXG4gICAgY29uc3QgbGluZXMgPSBwcm9tcHRzLnNwbGl0KCdcXG4nKS5maWx0ZXIobGluZSA9PiBsaW5lLnRyaW0oKSk7XHJcblxyXG4gICAgZm9yIChjb25zdCBsaW5lIG9mIGxpbmVzKSB7XHJcbiAgICAgIGNvbnN0IHRyaW1tZWRMaW5lID0gbGluZS50cmltKCk7XHJcblxyXG4gICAgICBpZiAodHJpbW1lZExpbmUuc3RhcnRzV2l0aCgnU1lTVEVNOicpKSB7XHJcbiAgICAgICAgcHJvY2Vzc2VkLnN5c3RlbV9pbnN0cnVjdGlvbnMgKz0gdHJpbW1lZExpbmUucmVwbGFjZSgnU1lTVEVNOicsICcnKS50cmltKCkgKyAnXFxuJztcclxuICAgICAgfSBlbHNlIGlmICh0cmltbWVkTGluZS5zdGFydHNXaXRoKCdCRUhBVklPUjonKSkge1xyXG4gICAgICAgIHByb2Nlc3NlZC5iZWhhdmlvcl9ndWlkZWxpbmVzICs9IHRyaW1tZWRMaW5lLnJlcGxhY2UoJ0JFSEFWSU9SOicsICcnKS50cmltKCkgKyAnXFxuJztcclxuICAgICAgfSBlbHNlIGlmICh0cmltbWVkTGluZS5pbmNsdWRlcygn4oaSJykgfHwgdHJpbW1lZExpbmUuaW5jbHVkZXMoJy0+JykpIHtcclxuICAgICAgICAvLyBQYXJzZSBleGFtcGxlOiBcIlVzZXIgaW5wdXQg4oaSIEV4cGVjdGVkIHJlc3BvbnNlXCJcclxuICAgICAgICBjb25zdCBzZXBhcmF0b3IgPSB0cmltbWVkTGluZS5pbmNsdWRlcygn4oaSJykgPyAn4oaSJyA6ICctPic7XHJcbiAgICAgICAgY29uc3QgcGFydHMgPSB0cmltbWVkTGluZS5zcGxpdChzZXBhcmF0b3IpO1xyXG4gICAgICAgIGlmIChwYXJ0cy5sZW5ndGggPj0gMikge1xyXG4gICAgICAgICAgY29uc3QgaW5wdXQgPSBwYXJ0c1swXS50cmltKCk7XHJcbiAgICAgICAgICBjb25zdCBvdXRwdXQgPSBwYXJ0cy5zbGljZSgxKS5qb2luKHNlcGFyYXRvcikudHJpbSgpO1xyXG4gICAgICAgICAgcHJvY2Vzc2VkLmV4YW1wbGVzLnB1c2goeyBpbnB1dCwgb3V0cHV0IH0pO1xyXG4gICAgICAgIH1cclxuICAgICAgfSBlbHNlIGlmICh0cmltbWVkTGluZS5sZW5ndGggPiAwKSB7XHJcbiAgICAgICAgLy8gR2VuZXJhbCBpbnN0cnVjdGlvblxyXG4gICAgICAgIHByb2Nlc3NlZC5nZW5lcmFsX2luc3RydWN0aW9ucyArPSB0cmltbWVkTGluZSArICdcXG4nO1xyXG4gICAgICB9XHJcbiAgICB9XHJcblxyXG4gICAgcmV0dXJuIHByb2Nlc3NlZDtcclxuICB9O1xyXG5cclxuICAvLyBIYW5kbGUgdHJhaW5pbmcgam9iIGNyZWF0aW9uIG9yIHVwZGF0ZVxyXG4gIGNvbnN0IGhhbmRsZVN0YXJ0VHJhaW5pbmcgPSBhc3luYyAoKSA9PiB7XHJcbiAgICBpZiAoIXNlbGVjdGVkQ29uZmlnSWQgfHwgIXRyYWluaW5nUHJvbXB0cy50cmltKCkpIHtcclxuICAgICAgc2V0RXJyb3IoJ1BsZWFzZSBzZWxlY3QgYW4gQVBJIGNvbmZpZ3VyYXRpb24gYW5kIHByb3ZpZGUgdHJhaW5pbmcgcHJvbXB0cy4nKTtcclxuICAgICAgcmV0dXJuO1xyXG4gICAgfVxyXG5cclxuICAgIC8vIFByZXZlbnQgbXVsdGlwbGUgc2ltdWx0YW5lb3VzIHRyYWluaW5nIG9wZXJhdGlvbnNcclxuICAgIGlmIChpc0xvYWRpbmcpIHtcclxuICAgICAgY29uc29sZS53YXJuKCdbVHJhaW5pbmddIE9wZXJhdGlvbiBhbHJlYWR5IGluIHByb2dyZXNzLCBpZ25vcmluZyBkdXBsaWNhdGUgcmVxdWVzdCcpO1xyXG4gICAgICByZXR1cm47XHJcbiAgICB9XHJcblxyXG4gICAgc2V0SXNMb2FkaW5nKHRydWUpO1xyXG4gICAgc2V0RXJyb3IobnVsbCk7XHJcbiAgICBzZXRTdWNjZXNzTWVzc2FnZShudWxsKTtcclxuXHJcbiAgICB0cnkge1xyXG4gICAgICAvLyBQcm9jZXNzIHRyYWluaW5nIHByb21wdHNcclxuICAgICAgY29uc3QgcHJvY2Vzc2VkUHJvbXB0cyA9IHByb2Nlc3NUcmFpbmluZ1Byb21wdHModHJhaW5pbmdQcm9tcHRzKTtcclxuXHJcbiAgICAgIC8vIEdlbmVyYXRlIGEgbWVhbmluZ2Z1bCBuYW1lIGJhc2VkIG9uIGNvbnRlbnRcclxuICAgICAgY29uc3QgY29uZmlnTmFtZSA9IGN1c3RvbUNvbmZpZ3MuZmluZChjID0+IGMuaWQgPT09IHNlbGVjdGVkQ29uZmlnSWQpPy5uYW1lIHx8ICdVbmtub3duIENvbmZpZyc7XHJcblxyXG4gICAgICAvLyBQcmVwYXJlIHRyYWluaW5nIGpvYiBkYXRhXHJcbiAgICAgIGNvbnN0IHRyYWluaW5nSm9iRGF0YSA9IHtcclxuICAgICAgICBjdXN0b21fYXBpX2NvbmZpZ19pZDogc2VsZWN0ZWRDb25maWdJZCxcclxuICAgICAgICBuYW1lOiBgJHtjb25maWdOYW1lfSBUcmFpbmluZyAtICR7bmV3IERhdGUoKS50b0xvY2FsZURhdGVTdHJpbmcoKX1gLFxyXG4gICAgICAgIGRlc2NyaXB0aW9uOiBgVHJhaW5pbmcgam9iIGZvciAke2NvbmZpZ05hbWV9IHdpdGggJHtwcm9jZXNzZWRQcm9tcHRzLmV4YW1wbGVzLmxlbmd0aH0gZXhhbXBsZXNgLFxyXG4gICAgICAgIHRyYWluaW5nX2RhdGE6IHtcclxuICAgICAgICAgIHByb2Nlc3NlZF9wcm9tcHRzOiBwcm9jZXNzZWRQcm9tcHRzLFxyXG4gICAgICAgICAgcmF3X3Byb21wdHM6IHRyYWluaW5nUHJvbXB0cy50cmltKCksXHJcbiAgICAgICAgICBsYXN0X3Byb21wdF91cGRhdGU6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKVxyXG4gICAgICAgIH0sXHJcbiAgICAgICAgcGFyYW1ldGVyczoge1xyXG4gICAgICAgICAgdHJhaW5pbmdfdHlwZTogJ3Byb21wdF9lbmdpbmVlcmluZycsXHJcbiAgICAgICAgICBjcmVhdGVkX3ZpYTogJ3RyYWluaW5nX3BhZ2UnLFxyXG4gICAgICAgICAgdmVyc2lvbjogJzEuMCdcclxuICAgICAgICB9XHJcbiAgICAgIH07XHJcblxyXG4gICAgICAvLyBVc2UgVVBTRVJUIHRvIGhhbmRsZSBib3RoIGNyZWF0ZSBhbmQgdXBkYXRlIHNjZW5hcmlvc1xyXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKCcvYXBpL3RyYWluaW5nL2pvYnMvdXBzZXJ0Jywge1xyXG4gICAgICAgIG1ldGhvZDogJ1BPU1QnLFxyXG4gICAgICAgIGhlYWRlcnM6IHsgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyB9LFxyXG4gICAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KHRyYWluaW5nSm9iRGF0YSlcclxuICAgICAgfSk7XHJcblxyXG4gICAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XHJcbiAgICAgICAgY29uc3QgZXJyb3JUZXh0ID0gYXdhaXQgcmVzcG9uc2UudGV4dCgpO1xyXG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ1tUcmFpbmluZ10gRmFpbGVkIHRvIHVwc2VydCB0cmFpbmluZyBqb2I6JywgZXJyb3JUZXh0KTtcclxuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYEZhaWxlZCB0byBzYXZlIHRyYWluaW5nIGpvYjogJHtyZXNwb25zZS5zdGF0dXN9ICR7ZXJyb3JUZXh0fWApO1xyXG4gICAgICB9XHJcblxyXG4gICAgICBjb25zdCByZXN1bHQgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XHJcbiAgICAgIGNvbnN0IGlzVXBkYXRlID0gcmVzdWx0Lm9wZXJhdGlvbiA9PT0gJ3VwZGF0ZWQnO1xyXG5cclxuICAgICAgY29uc29sZS5sb2coYFtUcmFpbmluZ10gU3VjY2Vzc2Z1bGx5ICR7aXNVcGRhdGUgPyAndXBkYXRlZCcgOiAnY3JlYXRlZCd9IHRyYWluaW5nIGpvYjpgLCByZXN1bHQuaWQpO1xyXG5cclxuICAgICAgLy8gU2hvdyBzdWNjZXNzIG1lc3NhZ2UgYmFzZWQgb24gb3BlcmF0aW9uIHR5cGVcclxuICAgICAgY29uc3Qgb3BlcmF0aW9uVGV4dCA9IGlzVXBkYXRlID8gJ3VwZGF0ZWQnIDogJ2VuaGFuY2VkJztcclxuICAgICAgY29uc3Qgb3BlcmF0aW9uRW1vamkgPSBpc1VwZGF0ZSA/ICfwn5SEJyA6ICfwn46JJztcclxuXHJcbiAgICAgIGNvbnN0IHN1Y2Nlc3NNZXNzYWdlID0gYCR7b3BlcmF0aW9uRW1vaml9IFByb21wdCBFbmdpbmVlcmluZyAke2lzVXBkYXRlID8gJ3VwZGF0ZWQnIDogJ2NvbXBsZXRlZCd9IHN1Y2Nlc3NmdWxseSFcXG5cXG5gICtcclxuICAgICAgICBgWW91ciBcIiR7Y29uZmlnTmFtZX1cIiBjb25maWd1cmF0aW9uIGhhcyBiZWVuICR7b3BlcmF0aW9uVGV4dH0gd2l0aDpcXG5gICtcclxuICAgICAgICBg4oCiICR7cHJvY2Vzc2VkUHJvbXB0cy5leGFtcGxlcy5sZW5ndGh9IHRyYWluaW5nIGV4YW1wbGVzXFxuYCArXHJcbiAgICAgICAgYOKAoiBDdXN0b20gc3lzdGVtIGluc3RydWN0aW9ucyBhbmQgYmVoYXZpb3IgZ3VpZGVsaW5lc1xcbmAgK1xyXG4gICAgICAgIGBcXG7inKggQWxsIGZ1dHVyZSBjaGF0cyB1c2luZyB0aGlzIGNvbmZpZ3VyYXRpb24gd2lsbCBhdXRvbWF0aWNhbGx5OlxcbmAgK1xyXG4gICAgICAgIGDigKIgRm9sbG93IHlvdXIgdHJhaW5pbmcgZXhhbXBsZXNcXG5gICtcclxuICAgICAgICBg4oCiIEFwcGx5IHlvdXIgYmVoYXZpb3IgZ3VpZGVsaW5lc1xcbmAgK1xyXG4gICAgICAgIGDigKIgTWFpbnRhaW4gY29uc2lzdGVudCBwZXJzb25hbGl0eSBhbmQgcmVzcG9uc2VzXFxuXFxuYCArXHJcbiAgICAgICAgYPCfmoAgVHJ5IGl0IG5vdyBpbiB0aGUgUGxheWdyb3VuZCB0byBzZWUgeW91ciAke2lzVXBkYXRlID8gJ3VwZGF0ZWQnIDogJ2VuaGFuY2VkJ30gbW9kZWwgaW4gYWN0aW9uIVxcblxcbmAgK1xyXG4gICAgICAgIGDwn5KhIFlvdXIgdHJhaW5pbmcgcHJvbXB0cyByZW1haW4gaGVyZSBzbyB5b3UgY2FuIG1vZGlmeSB0aGVtIGFueXRpbWUuYDtcclxuXHJcbiAgICAgIHNldFN1Y2Nlc3NNZXNzYWdlKHN1Y2Nlc3NNZXNzYWdlKTtcclxuXHJcbiAgICB9IGNhdGNoIChlcnI6IGFueSkge1xyXG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBpbiB0cmFpbmluZyBvcGVyYXRpb246JywgZXJyKTtcclxuICAgICAgc2V0RXJyb3IoYEZhaWxlZCB0byBjcmVhdGUgcHJvbXB0IGVuZ2luZWVyaW5nOiAke2Vyci5tZXNzYWdlfWApO1xyXG4gICAgfSBmaW5hbGx5IHtcclxuICAgICAgc2V0SXNMb2FkaW5nKGZhbHNlKTtcclxuICAgIH1cclxuICB9O1xyXG5cclxuICByZXR1cm4gKFxyXG4gICAgPGRpdiBjbGFzc05hbWU9XCJtaW4taC1zY3JlZW4gYmctWyNmYWY4ZjVdIHAtNlwiPlxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1heC13LTZ4bCBteC1hdXRvXCI+XHJcbiAgICAgICAgey8qIEhlYWRlciAqL31cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLThcIj5cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIG1iLTRcIj5cclxuICAgICAgICAgICAgPGgxIGNsYXNzTmFtZT1cInRleHQtNHhsIGZvbnQtYm9sZCB0ZXh0LWdyYXktOTAwXCI+QUkgVHJhaW5pbmcgJiBFbmhhbmNlbWVudDwvaDE+XHJcbiAgICAgICAgICAgIHtzdWJzY3JpcHRpb25TdGF0dXMgJiYgKFxyXG4gICAgICAgICAgICAgIDxUaWVyQmFkZ2UgdGllcj17c3Vic2NyaXB0aW9uU3RhdHVzLnRpZXJ9IHNpemU9XCJsZ1wiIC8+XHJcbiAgICAgICAgICAgICl9XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtbGcgdGV4dC1ncmF5LTYwMCBtYXgtdy0zeGxcIj5cclxuICAgICAgICAgICAgRW5oYW5jZSB5b3VyIEFJIG1vZGVscyB3aXRoIGN1c3RvbSBwcm9tcHRzIGFuZCBrbm93bGVkZ2UgZG9jdW1lbnRzLiBVcGxvYWQgeW91ciBwcm9wcmlldGFyeSBjb250ZW50IHRvIGNyZWF0ZSBkb21haW4tc3BlY2lmaWMgQUkgYXNzaXN0YW50cy5cclxuICAgICAgICAgIDwvcD5cclxuICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgey8qIEVycm9yL1N1Y2Nlc3MgTWVzc2FnZXMgKi99XHJcbiAgICAgICAge2Vycm9yICYmIChcclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItNiBiZy1yZWQtNTAgYm9yZGVyIGJvcmRlci1yZWQtMjAwIHJvdW5kZWQteGwgcC00XCI+XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCI+XHJcbiAgICAgICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJ3LTUgaC01IHRleHQtcmVkLTYwMFwiIGZpbGw9XCJub25lXCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiPlxyXG4gICAgICAgICAgICAgICAgPHBhdGggc3Ryb2tlTGluZWNhcD1cInJvdW5kXCIgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiIHN0cm9rZVdpZHRoPXsyfSBkPVwiTTEyIDh2NG0wIDRoLjAxTTIxIDEyYTkgOSAwIDExLTE4IDAgOSA5IDAgMDExOCAwelwiIC8+XHJcbiAgICAgICAgICAgICAgPC9zdmc+XHJcbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1yZWQtODAwIHRleHQtc20gZm9udC1tZWRpdW1cIj57ZXJyb3J9PC9wPlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICl9XHJcblxyXG4gICAgICAgIHtzdWNjZXNzTWVzc2FnZSAmJiAoXHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTYgYmctZ3JlZW4tNTAgYm9yZGVyIGJvcmRlci1ncmVlbi0yMDAgcm91bmRlZC14bCBwLTRcIj5cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTJcIj5cclxuICAgICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctNSBoLTUgdGV4dC1ncmVlbi02MDBcIiBmaWxsPVwibm9uZVwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIj5cclxuICAgICAgICAgICAgICAgIDxwYXRoIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIiBzdHJva2VXaWR0aD17Mn0gZD1cIk01IDEzbDQgNEwxOSA3XCIgLz5cclxuICAgICAgICAgICAgICA8L3N2Zz5cclxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyZWVuLTgwMCB0ZXh0LXNtIGZvbnQtbWVkaXVtXCI+e3N1Y2Nlc3NNZXNzYWdlfTwvcD5cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICApfVxyXG5cclxuICAgICAgICB7LyogRG9jdW1lbnQgVXBsb2FkIFNlY3Rpb24gKi99XHJcbiAgICAgICAgPFRpZXJHdWFyZFxyXG4gICAgICAgICAgZmVhdHVyZT1cImtub3dsZWRnZV9iYXNlXCJcclxuICAgICAgICAgIGN1c3RvbU1lc3NhZ2U9XCJLbm93bGVkZ2UgYmFzZSBkb2N1bWVudCB1cGxvYWQgaXMgYXZhaWxhYmxlIHN0YXJ0aW5nIHdpdGggdGhlIFByb2Zlc3Npb25hbCBwbGFuLiBVcGxvYWQgZG9jdW1lbnRzIHRvIGVuaGFuY2UgeW91ciBBSSB3aXRoIHByb3ByaWV0YXJ5IGtub3dsZWRnZSBhbmQgY3JlYXRlIGRvbWFpbi1zcGVjaWZpYyBhc3Npc3RhbnRzLlwiXHJcbiAgICAgICAgPlxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSByb3VuZGVkLTJ4bCBzaGFkb3ctbGcgcC04IG1iLThcIj5cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTMgbWItNlwiPlxyXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy04IGgtOCBiZy1vcmFuZ2UtMTAwIHJvdW5kZWQtbGcgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cclxuICAgICAgICAgICAgICAgIDxzdmcgY2xhc3NOYW1lPVwidy01IGgtNSB0ZXh0LW9yYW5nZS02MDBcIiBmaWxsPVwibm9uZVwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIj5cclxuICAgICAgICAgICAgICAgICAgPHBhdGggc3Ryb2tlTGluZWNhcD1cInJvdW5kXCIgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiIHN0cm9rZVdpZHRoPXsyfSBkPVwiTTcgMTZhNCA0IDAgMDEtLjg4LTcuOTAzQTUgNSAwIDExMTUuOSA2TDE2IDZhNSA1IDAgMDExIDkuOU0xNSAxM2wtMy0zbTAgMGwtMyAzbTMtM3YxMlwiIC8+XHJcbiAgICAgICAgICAgICAgICA8L3N2Zz5cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICA8ZGl2PlxyXG4gICAgICAgICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LWdyYXktOTAwXCI+S25vd2xlZGdlIERvY3VtZW50czwvaDI+XHJcbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDBcIj5VcGxvYWQgZG9jdW1lbnRzIHRvIGVuaGFuY2UgeW91ciBBSSB3aXRoIHByb3ByaWV0YXJ5IGtub3dsZWRnZTwvcD5cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgICA8RG9jdW1lbnRVcGxvYWRcclxuICAgICAgICAgICAgICBjb25maWdJZD17c2VsZWN0ZWRDb25maWdJZH1cclxuICAgICAgICAgICAgICBvbkRvY3VtZW50VXBsb2FkZWQ9eygpID0+IHtcclxuICAgICAgICAgICAgICAgIC8vIE9wdGlvbmFsbHkgcmVmcmVzaCBzb21ldGhpbmcgb3Igc2hvdyBhIG1lc3NhZ2VcclxuICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKCdEb2N1bWVudCB1cGxvYWRlZCBzdWNjZXNzZnVsbHknKTtcclxuICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAvPlxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgPC9UaWVyR3VhcmQ+XHJcblxyXG4gICAgICAgIHsvKiBUcmFpbmluZyBGb3JtICovfVxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgcm91bmRlZC0yeGwgc2hhZG93LWxnIHAtOFwiPlxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTMgbWItNlwiPlxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctOCBoLTggYmctYmx1ZS0xMDAgcm91bmRlZC1sZyBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxyXG4gICAgICAgICAgICAgIDxzdmcgY2xhc3NOYW1lPVwidy01IGgtNSB0ZXh0LWJsdWUtNjAwXCIgZmlsbD1cIm5vbmVcIiBzdHJva2U9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCI+XHJcbiAgICAgICAgICAgICAgICA8cGF0aCBzdHJva2VMaW5lY2FwPVwicm91bmRcIiBzdHJva2VMaW5lam9pbj1cInJvdW5kXCIgc3Ryb2tlV2lkdGg9ezJ9IGQ9XCJNMTEgNUg2YTIgMiAwIDAwLTIgMnYxMWEyIDIgMCAwMDIgMmgxMWEyIDIgMCAwMDItMnYtNW0tMS40MTQtOS40MTRhMiAyIDAgMTEyLjgyOCAyLjgyOEwxMS44MjggMTVIOXYtMi44MjhsOC41ODYtOC41ODZ6XCIgLz5cclxuICAgICAgICAgICAgICA8L3N2Zz5cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgIDxkaXY+XHJcbiAgICAgICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LWdyYXktOTAwXCI+Q3VzdG9tIFByb21wdHM8L2gyPlxyXG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMFwiPkRlZmluZSBiZWhhdmlvciwgZXhhbXBsZXMsIGFuZCBpbnN0cnVjdGlvbnMgZm9yIHlvdXIgQUk8L3A+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LThcIj5cclxuICAgICAgICAgICAgey8qIEFQSSBDb25maWd1cmF0aW9uIFNlbGVjdGlvbiAqL31cclxuICAgICAgICAgICAgPGRpdj5cclxuICAgICAgICAgICAgICA8bGFiZWwgaHRtbEZvcj1cImNvbmZpZ1NlbGVjdFwiIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMCBtYi0yXCI+XHJcbiAgICAgICAgICAgICAgICBTZWxlY3QgQVBJIENvbmZpZ3VyYXRpb25cclxuICAgICAgICAgICAgICA8L2xhYmVsPlxyXG4gICAgICAgICAgICAgIDxzZWxlY3RcclxuICAgICAgICAgICAgICAgIGlkPVwiY29uZmlnU2VsZWN0XCJcclxuICAgICAgICAgICAgICAgIHZhbHVlPXtzZWxlY3RlZENvbmZpZ0lkfVxyXG4gICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRTZWxlY3RlZENvbmZpZ0lkKGUudGFyZ2V0LnZhbHVlKX1cclxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBweC00IHB5LTMgYm9yZGVyIGJvcmRlci1ncmF5LTIwMCByb3VuZGVkLXhsIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLW9yYW5nZS01MDAgZm9jdXM6Ym9yZGVyLW9yYW5nZS01MDAgdHJhbnNpdGlvbi1jb2xvcnMgYmctd2hpdGUgdGV4dC1zbVwiXHJcbiAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIlwiPkNob29zZSB3aGljaCBtb2RlbCB0byB0cmFpbi4uLjwvb3B0aW9uPlxyXG4gICAgICAgICAgICAgICAge2N1c3RvbUNvbmZpZ3MubWFwKChjb25maWcpID0+IChcclxuICAgICAgICAgICAgICAgICAgPG9wdGlvbiBrZXk9e2NvbmZpZy5pZH0gdmFsdWU9e2NvbmZpZy5pZH0+XHJcbiAgICAgICAgICAgICAgICAgICAge2NvbmZpZy5uYW1lfVxyXG4gICAgICAgICAgICAgICAgICA8L29wdGlvbj5cclxuICAgICAgICAgICAgICAgICkpfVxyXG4gICAgICAgICAgICAgIDwvc2VsZWN0PlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICAgIHsvKiBUcmFpbmluZyBQcm9tcHRzICovfVxyXG4gICAgICAgICAgICA8ZGl2PlxyXG4gICAgICAgICAgICAgIDxsYWJlbCBodG1sRm9yPVwidHJhaW5pbmdQcm9tcHRzXCIgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwIG1iLTJcIj5cclxuICAgICAgICAgICAgICAgIEN1c3RvbSBQcm9tcHRzICYgSW5zdHJ1Y3Rpb25zXHJcbiAgICAgICAgICAgICAgPC9sYWJlbD5cclxuICAgICAgICAgICAgICA8dGV4dGFyZWFcclxuICAgICAgICAgICAgICAgIGlkPVwidHJhaW5pbmdQcm9tcHRzXCJcclxuICAgICAgICAgICAgICAgIHZhbHVlPXt0cmFpbmluZ1Byb21wdHN9XHJcbiAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldFRyYWluaW5nUHJvbXB0cyhlLnRhcmdldC52YWx1ZSl9XHJcbiAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj17YEVudGVyIHlvdXIgdHJhaW5pbmcgcHJvbXB0cyB1c2luZyB0aGVzZSBmb3JtYXRzOlxyXG5cclxuU1lTVEVNOiBZb3UgYXJlIGEgaGVscGZ1bCBjdXN0b21lciBzZXJ2aWNlIGFnZW50IGZvciBvdXIgY29tcGFueVxyXG5CRUhBVklPUjogQWx3YXlzIGJlIHBvbGl0ZSBhbmQgb2ZmZXIgc29sdXRpb25zXHJcblxyXG5Vc2VyIGFza3MgYWJvdXQgcmV0dXJucyDihpIgSSdkIGJlIGhhcHB5IHRvIGhlbHAgd2l0aCB5b3VyIHJldHVybiEgTGV0IG1lIGNoZWNrIG91ciBwb2xpY3kgZm9yIHlvdS5cclxuQ3VzdG9tZXIgaXMgZnJ1c3RyYXRlZCDihpIgSSB1bmRlcnN0YW5kIHlvdXIgZnJ1c3RyYXRpb24uIExldCBtZSBzZWUgaG93IEkgY2FuIHJlc29sdmUgdGhpcyBmb3IgeW91LlxyXG5cclxuR2VuZXJhbCBpbnN0cnVjdGlvbnMgY2FuIGJlIHdyaXR0ZW4gYXMgcmVndWxhciB0ZXh0LmB9XHJcbiAgICAgICAgICAgICAgICByb3dzPXsxMn1cclxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBweC00IHB5LTMgYm9yZGVyIGJvcmRlci1ncmF5LTIwMCByb3VuZGVkLXhsIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLW9yYW5nZS01MDAgZm9jdXM6Ym9yZGVyLW9yYW5nZS01MDAgdHJhbnNpdGlvbi1jb2xvcnMgYmctd2hpdGUgdGV4dC1zbSByZXNpemUtbm9uZSBmb250LW1vbm9cIlxyXG4gICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC0zIGJnLWJsdWUtNTAgYm9yZGVyIGJvcmRlci1ibHVlLTIwMCByb3VuZGVkLWxnIHAtM1wiPlxyXG4gICAgICAgICAgICAgICAgPGg0IGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ibHVlLTkwMCBtYi0yXCI+VHJhaW5pbmcgRm9ybWF0IEd1aWRlOjwvaDQ+XHJcbiAgICAgICAgICAgICAgICA8dWwgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWJsdWUtODAwIHNwYWNlLXktMVwiPlxyXG4gICAgICAgICAgICAgICAgICA8bGk+PHN0cm9uZz5TWVNURU06PC9zdHJvbmc+IENvcmUgaW5zdHJ1Y3Rpb25zIGZvciB0aGUgQUkncyByb2xlIGFuZCBwZXJzb25hbGl0eTwvbGk+XHJcbiAgICAgICAgICAgICAgICAgIDxsaT48c3Ryb25nPkJFSEFWSU9SOjwvc3Ryb25nPiBHdWlkZWxpbmVzIGZvciBob3cgdGhlIEFJIHNob3VsZCBiZWhhdmU8L2xpPlxyXG4gICAgICAgICAgICAgICAgICA8bGk+PHN0cm9uZz5FeGFtcGxlczo8L3N0cm9uZz4gVXNlIFwiVXNlciBpbnB1dCDihpIgRXhwZWN0ZWQgcmVzcG9uc2VcIiBmb3JtYXQ8L2xpPlxyXG4gICAgICAgICAgICAgICAgICA8bGk+PHN0cm9uZz5HZW5lcmFsOjwvc3Ryb25nPiBBbnkgb3RoZXIgaW5zdHJ1Y3Rpb25zIHdyaXR0ZW4gYXMgbm9ybWFsIHRleHQ8L2xpPlxyXG4gICAgICAgICAgICAgICAgPC91bD5cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgPC9kaXY+XHJcblxyXG5cclxuXHJcbiAgICAgICAgICAgIHsvKiBBY3Rpb24gQnV0dG9ucyAqL31cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktYmV0d2VlbiBpdGVtcy1jZW50ZXIgcHQtNiBib3JkZXItdCBib3JkZXItZ3JheS0yMDBcIj5cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggc3BhY2UteC0zXCI+XHJcbiAgICAgICAgICAgICAgICA8YnV0dG9uXHJcbiAgICAgICAgICAgICAgICAgIHR5cGU9XCJidXR0b25cIlxyXG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJidG4tc2Vjb25kYXJ5XCJcclxuICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgIGlmIChjb25maXJtKCdDbGVhciBhbGwgdHJhaW5pbmcgcHJvbXB0cz8nKSkge1xyXG4gICAgICAgICAgICAgICAgICAgICAgc2V0VHJhaW5pbmdQcm9tcHRzKCcnKTtcclxuICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgIENsZWFyIEZvcm1cclxuICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgICAgICA8YnV0dG9uXHJcbiAgICAgICAgICAgICAgICB0eXBlPVwiYnV0dG9uXCJcclxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZVN0YXJ0VHJhaW5pbmd9XHJcbiAgICAgICAgICAgICAgICBkaXNhYmxlZD17IXNlbGVjdGVkQ29uZmlnSWQgfHwgIXRyYWluaW5nUHJvbXB0cy50cmltKCkgfHwgaXNMb2FkaW5nfVxyXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYnRuLXByaW1hcnkgZGlzYWJsZWQ6b3BhY2l0eS01MCBkaXNhYmxlZDpjdXJzb3Itbm90LWFsbG93ZWRcIlxyXG4gICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgIHtpc0xvYWRpbmcgPyAoXHJcbiAgICAgICAgICAgICAgICAgIDw+XHJcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhbmltYXRlLXNwaW4gcm91bmRlZC1mdWxsIGgtNCB3LTQgYm9yZGVyLWItMiBib3JkZXItd2hpdGUgbXItMlwiPjwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgIFByb2Nlc3NpbmcgUHJvbXB0cy4uLlxyXG4gICAgICAgICAgICAgICAgICA8Lz5cclxuICAgICAgICAgICAgICAgICkgOiAoXHJcbiAgICAgICAgICAgICAgICAgICdTYXZlIFByb21wdHMnXHJcbiAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgIDwvYnV0dG9uPlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICA8L2Rpdj5cclxuXHJcblxyXG4gICAgPC9kaXY+XHJcbiAgKTtcclxufSJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsIkRvY3VtZW50VXBsb2FkIiwiVGllckd1YXJkIiwiVGllckJhZGdlIiwidXNlU3Vic2NyaXB0aW9uIiwiVHJhaW5pbmdQYWdlIiwic3Vic2NyaXB0aW9uU3RhdHVzIiwiY3VzdG9tQ29uZmlncyIsInNldEN1c3RvbUNvbmZpZ3MiLCJzZWxlY3RlZENvbmZpZ0lkIiwic2V0U2VsZWN0ZWRDb25maWdJZCIsInRyYWluaW5nSm9icyIsInNldFRyYWluaW5nSm9icyIsImlzTG9hZGluZyIsInNldElzTG9hZGluZyIsImVycm9yIiwic2V0RXJyb3IiLCJzdWNjZXNzTWVzc2FnZSIsInNldFN1Y2Nlc3NNZXNzYWdlIiwidHJhaW5pbmdQcm9tcHRzIiwic2V0VHJhaW5pbmdQcm9tcHRzIiwibG9hZEV4aXN0aW5nVHJhaW5pbmdEYXRhIiwiY29uZmlnSWQiLCJqb2JzUmVzcG9uc2UiLCJmZXRjaCIsIm9rIiwiam9icyIsImpzb24iLCJsZW5ndGgiLCJsYXRlc3RKb2IiLCJ0cmFpbmluZ19kYXRhIiwicmF3X3Byb21wdHMiLCJlcnIiLCJjb25zb2xlIiwid2FybiIsImZldGNoQ29uZmlncyIsInJlc3BvbnNlIiwiRXJyb3IiLCJkYXRhIiwiaWQiLCJtZXNzYWdlIiwicHJvY2Vzc1RyYWluaW5nUHJvbXB0cyIsInByb21wdHMiLCJwcm9jZXNzZWQiLCJzeXN0ZW1faW5zdHJ1Y3Rpb25zIiwiZXhhbXBsZXMiLCJiZWhhdmlvcl9ndWlkZWxpbmVzIiwiZ2VuZXJhbF9pbnN0cnVjdGlvbnMiLCJsaW5lcyIsInNwbGl0IiwiZmlsdGVyIiwibGluZSIsInRyaW0iLCJ0cmltbWVkTGluZSIsInN0YXJ0c1dpdGgiLCJyZXBsYWNlIiwiaW5jbHVkZXMiLCJzZXBhcmF0b3IiLCJwYXJ0cyIsImlucHV0Iiwib3V0cHV0Iiwic2xpY2UiLCJqb2luIiwicHVzaCIsImhhbmRsZVN0YXJ0VHJhaW5pbmciLCJwcm9jZXNzZWRQcm9tcHRzIiwiY29uZmlnTmFtZSIsImZpbmQiLCJjIiwibmFtZSIsInRyYWluaW5nSm9iRGF0YSIsImN1c3RvbV9hcGlfY29uZmlnX2lkIiwiRGF0ZSIsInRvTG9jYWxlRGF0ZVN0cmluZyIsImRlc2NyaXB0aW9uIiwicHJvY2Vzc2VkX3Byb21wdHMiLCJsYXN0X3Byb21wdF91cGRhdGUiLCJ0b0lTT1N0cmluZyIsInBhcmFtZXRlcnMiLCJ0cmFpbmluZ190eXBlIiwiY3JlYXRlZF92aWEiLCJ2ZXJzaW9uIiwibWV0aG9kIiwiaGVhZGVycyIsImJvZHkiLCJKU09OIiwic3RyaW5naWZ5IiwiZXJyb3JUZXh0IiwidGV4dCIsInN0YXR1cyIsInJlc3VsdCIsImlzVXBkYXRlIiwib3BlcmF0aW9uIiwibG9nIiwib3BlcmF0aW9uVGV4dCIsIm9wZXJhdGlvbkVtb2ppIiwiZGl2IiwiY2xhc3NOYW1lIiwiaDEiLCJ0aWVyIiwic2l6ZSIsInAiLCJzdmciLCJmaWxsIiwic3Ryb2tlIiwidmlld0JveCIsInBhdGgiLCJzdHJva2VMaW5lY2FwIiwic3Ryb2tlTGluZWpvaW4iLCJzdHJva2VXaWR0aCIsImQiLCJmZWF0dXJlIiwiY3VzdG9tTWVzc2FnZSIsImgyIiwib25Eb2N1bWVudFVwbG9hZGVkIiwibGFiZWwiLCJodG1sRm9yIiwic2VsZWN0IiwidmFsdWUiLCJvbkNoYW5nZSIsImUiLCJ0YXJnZXQiLCJvcHRpb24iLCJtYXAiLCJjb25maWciLCJ0ZXh0YXJlYSIsInBsYWNlaG9sZGVyIiwicm93cyIsImg0IiwidWwiLCJsaSIsInN0cm9uZyIsImJ1dHRvbiIsInR5cGUiLCJvbkNsaWNrIiwiY29uZmlybSIsImRpc2FibGVkIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/training/page.tsx\n"));

/***/ })

});