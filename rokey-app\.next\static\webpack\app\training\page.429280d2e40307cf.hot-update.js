"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/training/page",{

/***/ "(app-pages-browser)/./src/app/training/page.tsx":
/*!***********************************!*\
  !*** ./src/app/training/page.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TrainingPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_DocumentUpload__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/DocumentUpload */ \"(app-pages-browser)/./src/components/DocumentUpload.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction TrainingPage() {\n    _s();\n    // Confirmation modal hook\n    // State management\n    const [customConfigs, setCustomConfigs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedConfigId, setSelectedConfigId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [trainingJobs, setTrainingJobs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [successMessage, setSuccessMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Prompt engineering form state\n    const [trainingPrompts, setTrainingPrompts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // Load existing training data for a configuration\n    const loadExistingTrainingData = async (configId)=>{\n        if (!configId) return;\n        try {\n            // Load training jobs\n            const jobsResponse = await fetch(\"/api/training/jobs?custom_api_config_id=\".concat(configId));\n            if (jobsResponse.ok) {\n                const jobs = await jobsResponse.json();\n                if (jobs.length > 0) {\n                    var _latestJob_training_data;\n                    const latestJob = jobs[0];\n                    // Load training prompts\n                    if ((_latestJob_training_data = latestJob.training_data) === null || _latestJob_training_data === void 0 ? void 0 : _latestJob_training_data.raw_prompts) {\n                        setTrainingPrompts(latestJob.training_data.raw_prompts);\n                    }\n                }\n            }\n        } catch (err) {\n            console.warn('Failed to load existing training data:', err);\n        }\n    };\n    // Fetch custom API configs on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TrainingPage.useEffect\": ()=>{\n            const fetchConfigs = {\n                \"TrainingPage.useEffect.fetchConfigs\": async ()=>{\n                    try {\n                        const response = await fetch('/api/custom-configs');\n                        if (!response.ok) {\n                            throw new Error('Failed to fetch configurations');\n                        }\n                        const data = await response.json();\n                        setCustomConfigs(data);\n                        if (data.length > 0) {\n                            setSelectedConfigId(data[0].id);\n                            loadExistingTrainingData(data[0].id);\n                        }\n                    } catch (err) {\n                        setError(\"Failed to load configurations: \".concat(err.message));\n                    }\n                }\n            }[\"TrainingPage.useEffect.fetchConfigs\"];\n            fetchConfigs();\n        }\n    }[\"TrainingPage.useEffect\"], []);\n    // Load training data when configuration changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TrainingPage.useEffect\": ()=>{\n            if (selectedConfigId) {\n                loadExistingTrainingData(selectedConfigId);\n            }\n        }\n    }[\"TrainingPage.useEffect\"], [\n        selectedConfigId\n    ]);\n    // Process training prompts into structured format\n    const processTrainingPrompts = (prompts)=>{\n        const processed = {\n            system_instructions: '',\n            examples: [],\n            behavior_guidelines: '',\n            general_instructions: ''\n        };\n        const lines = prompts.split('\\n').filter((line)=>line.trim());\n        for (const line of lines){\n            const trimmedLine = line.trim();\n            if (trimmedLine.startsWith('SYSTEM:')) {\n                processed.system_instructions += trimmedLine.replace('SYSTEM:', '').trim() + '\\n';\n            } else if (trimmedLine.startsWith('BEHAVIOR:')) {\n                processed.behavior_guidelines += trimmedLine.replace('BEHAVIOR:', '').trim() + '\\n';\n            } else if (trimmedLine.includes('→') || trimmedLine.includes('->')) {\n                // Parse example: \"User input → Expected response\"\n                const separator = trimmedLine.includes('→') ? '→' : '->';\n                const parts = trimmedLine.split(separator);\n                if (parts.length >= 2) {\n                    const input = parts[0].trim();\n                    const output = parts.slice(1).join(separator).trim();\n                    processed.examples.push({\n                        input,\n                        output\n                    });\n                }\n            } else if (trimmedLine.length > 0) {\n                // General instruction\n                processed.general_instructions += trimmedLine + '\\n';\n            }\n        }\n        return processed;\n    };\n    // Handle training job creation or update\n    const handleStartTraining = async ()=>{\n        if (!selectedConfigId || !trainingPrompts.trim()) {\n            setError('Please select an API configuration and provide training prompts.');\n            return;\n        }\n        // Prevent multiple simultaneous training operations\n        if (isLoading) {\n            console.warn('[Training] Operation already in progress, ignoring duplicate request');\n            return;\n        }\n        setIsLoading(true);\n        setError(null);\n        setSuccessMessage(null);\n        try {\n            var _customConfigs_find;\n            // Process training prompts\n            const processedPrompts = processTrainingPrompts(trainingPrompts);\n            // Generate a meaningful name based on content\n            const configName = ((_customConfigs_find = customConfigs.find((c)=>c.id === selectedConfigId)) === null || _customConfigs_find === void 0 ? void 0 : _customConfigs_find.name) || 'Unknown Config';\n            // Prepare training job data\n            const trainingJobData = {\n                custom_api_config_id: selectedConfigId,\n                name: \"\".concat(configName, \" Training - \").concat(new Date().toLocaleDateString()),\n                description: \"Training job for \".concat(configName, \" with \").concat(processedPrompts.examples.length, \" examples\"),\n                training_data: {\n                    processed_prompts: processedPrompts,\n                    raw_prompts: trainingPrompts.trim(),\n                    last_prompt_update: new Date().toISOString()\n                },\n                parameters: {\n                    training_type: 'prompt_engineering',\n                    created_via: 'training_page',\n                    version: '1.0'\n                }\n            };\n            // Use UPSERT to handle both create and update scenarios\n            const response = await fetch('/api/training/jobs/upsert', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(trainingJobData)\n            });\n            if (!response.ok) {\n                const errorText = await response.text();\n                console.error('[Training] Failed to upsert training job:', errorText);\n                throw new Error(\"Failed to save training job: \".concat(response.status, \" \").concat(errorText));\n            }\n            const result = await response.json();\n            const isUpdate = result.operation === 'updated';\n            console.log(\"[Training] Successfully \".concat(isUpdate ? 'updated' : 'created', \" training job:\"), result.id);\n            // Show success message based on operation type\n            const operationText = isUpdate ? 'updated' : 'enhanced';\n            const operationEmoji = isUpdate ? '🔄' : '🎉';\n            const successMessage = \"\".concat(operationEmoji, \" Prompt Engineering \").concat(isUpdate ? 'updated' : 'completed', \" successfully!\\n\\n\") + 'Your \"'.concat(configName, '\" configuration has been ').concat(operationText, \" with:\\n\") + \"• \".concat(processedPrompts.examples.length, \" training examples\\n\") + \"• Custom system instructions and behavior guidelines\\n\" + \"\\n✨ All future chats using this configuration will automatically:\\n\" + \"• Follow your training examples\\n\" + \"• Apply your behavior guidelines\\n\" + \"• Maintain consistent personality and responses\\n\\n\" + \"\\uD83D\\uDE80 Try it now in the Playground to see your \".concat(isUpdate ? 'updated' : 'enhanced', \" model in action!\\n\\n\") + \"\\uD83D\\uDCA1 Your training prompts remain here so you can modify them anytime.\";\n            setSuccessMessage(successMessage);\n        } catch (err) {\n            console.error('Error in training operation:', err);\n            setError(\"Failed to create prompt engineering: \".concat(err.message));\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-[#faf8f5] p-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-6xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-4xl font-bold text-gray-900 mb-4\",\n                            children: \"AI Training & Enhancement\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                            lineNumber: 214,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg text-gray-600 max-w-3xl\",\n                            children: \"Enhance your AI models with custom prompts and knowledge documents. Upload your proprietary content to create domain-specific AI assistants.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                            lineNumber: 215,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                    lineNumber: 213,\n                    columnNumber: 9\n                }, this),\n                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6 bg-red-50 border border-red-200 rounded-xl p-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-5 h-5 text-red-600\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                    lineNumber: 225,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                lineNumber: 224,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-red-800 text-sm font-medium\",\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                lineNumber: 227,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                        lineNumber: 223,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                    lineNumber: 222,\n                    columnNumber: 11\n                }, this),\n                successMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6 bg-green-50 border border-green-200 rounded-xl p-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-5 h-5 text-green-600\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M5 13l4 4L19 7\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                    lineNumber: 236,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                lineNumber: 235,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-green-800 text-sm font-medium\",\n                                children: successMessage\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                lineNumber: 238,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                        lineNumber: 234,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                    lineNumber: 233,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-2xl shadow-lg p-8 mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3 mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-5 h-5 text-orange-600\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                            lineNumber: 248,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                    lineNumber: 246,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-2xl font-bold text-gray-900\",\n                                            children: \"Knowledge Documents\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                            lineNumber: 252,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"Upload documents to enhance your AI with proprietary knowledge\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                            lineNumber: 253,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                    lineNumber: 251,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                            lineNumber: 245,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DocumentUpload__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            configId: selectedConfigId,\n                            onDocumentUploaded: ()=>{\n                                // Optionally refresh something or show a message\n                                console.log('Document uploaded successfully');\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                            lineNumber: 257,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                    lineNumber: 244,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-2xl shadow-lg p-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3 mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-5 h-5 text-blue-600\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                            lineNumber: 271,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                        lineNumber: 270,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                    lineNumber: 269,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-2xl font-bold text-gray-900\",\n                                            children: \"Custom Prompts\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                            lineNumber: 275,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"Define behavior, examples, and instructions for your AI\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                            lineNumber: 276,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                    lineNumber: 274,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                            lineNumber: 268,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"configSelect\",\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Select API Configuration\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                            lineNumber: 283,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            id: \"configSelect\",\n                                            value: selectedConfigId,\n                                            onChange: (e)=>setSelectedConfigId(e.target.value),\n                                            className: \"w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors bg-white text-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"\",\n                                                    children: \"Choose which model to train...\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                    lineNumber: 292,\n                                                    columnNumber: 17\n                                                }, this),\n                                                customConfigs.map((config)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: config.id,\n                                                        children: config.name\n                                                    }, config.id, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                        lineNumber: 294,\n                                                        columnNumber: 19\n                                                    }, this))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                            lineNumber: 286,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                    lineNumber: 282,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"trainingPrompts\",\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Custom Prompts & Instructions\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                            lineNumber: 303,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            id: \"trainingPrompts\",\n                                            value: trainingPrompts,\n                                            onChange: (e)=>setTrainingPrompts(e.target.value),\n                                            placeholder: \"Enter your training prompts using these formats:\\n\\nSYSTEM: You are a helpful customer service agent for our company\\nBEHAVIOR: Always be polite and offer solutions\\n\\nUser asks about returns → I'd be happy to help with your return! Let me check our policy for you.\\nCustomer is frustrated → I understand your frustration. Let me see how I can resolve this for you.\\n\\nGeneral instructions can be written as regular text.\",\n                                            rows: 12,\n                                            className: \"w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors bg-white text-sm resize-none font-mono\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                            lineNumber: 306,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-3 bg-blue-50 border border-blue-200 rounded-lg p-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"text-sm font-medium text-blue-900 mb-2\",\n                                                    children: \"Training Format Guide:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                    lineNumber: 323,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"text-xs text-blue-800 space-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"SYSTEM:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                                    lineNumber: 325,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \" Core instructions for the AI's role and personality\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                            lineNumber: 325,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"BEHAVIOR:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                                    lineNumber: 326,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \" Guidelines for how the AI should behave\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                            lineNumber: 326,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"Examples:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                                    lineNumber: 327,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                ' Use \"User input → Expected response\" format'\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                            lineNumber: 327,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"General:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                                    lineNumber: 328,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \" Any other instructions written as normal text\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                            lineNumber: 328,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                    lineNumber: 324,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                            lineNumber: 322,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                    lineNumber: 302,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center pt-6 border-t border-gray-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                className: \"btn-secondary\",\n                                                onClick: ()=>{\n                                                    if (confirm('Clear all training prompts?')) {\n                                                        setTrainingPrompts('');\n                                                    }\n                                                },\n                                                children: \"Clear Form\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                lineNumber: 338,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                            lineNumber: 337,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: handleStartTraining,\n                                            disabled: !selectedConfigId || !trainingPrompts.trim() || isLoading,\n                                            className: \"btn-primary disabled:opacity-50 disabled:cursor-not-allowed\",\n                                            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                        lineNumber: 359,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"Processing Prompts...\"\n                                                ]\n                                            }, void 0, true) : 'Save Prompts'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                            lineNumber: 351,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                    lineNumber: 336,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                            lineNumber: 280,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                    lineNumber: 267,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n            lineNumber: 211,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n        lineNumber: 210,\n        columnNumber: 5\n    }, this);\n}\n_s(TrainingPage, \"gPykGrUFE/59i+jPUeSFKtCLwvY=\");\n_c = TrainingPage;\nvar _c;\n$RefreshReg$(_c, \"TrainingPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/training/page.tsx\n"));

/***/ })

});